import type { PayloadAction } from '@reduxjs/toolkit';

import { createSlice } from '@reduxjs/toolkit';

export interface LiveTranscriptionState {
  selectedDevice: string | null;
  isLiveTranscription: boolean;
}

export const liveTranscriptionInitialState: LiveTranscriptionState = {
  selectedDevice: null,
  isLiveTranscription: false,
};

export const liveTranscriptionSlice = createSlice({
  name: 'liveTranscription',
  initialState: liveTranscriptionInitialState,
  reducers: {
    setSelectedDevice: (state, action: PayloadAction<string | null>) => {
      state.selectedDevice = action.payload;
    },
    setIsLiveTranscription: (state, action: PayloadAction<boolean>) => {
      state.isLiveTranscription = action.payload;
    },
  },
});

export const { setSelectedDevice, setIsLiveTranscription } = liveTranscriptionSlice.actions;

export default liveTranscriptionSlice.reducer;
