import type { Resource, LiveTranscriptionSegment } from 'src/types';

import { useState, useEffect, useCallback } from 'react';

import { Box } from '@mui/material';

import useResponsive from 'src/hooks/use-responsive';

import { useAppSelector } from 'src/store';
import { selectIsLiveTranscription } from 'src/store/slices/live-transcription/selectors';

import ChatPanel from 'src/sections/projects/components/chat-panel';
import FilesPanel from 'src/sections/projects/components/files-panel';
import NotesPanel from 'src/sections/projects/components/notes-panel';
import LiveTranscriptionPanel from 'src/sections/projects/components/live-transcription-panel';

import AidaSuggestionsPanel from '../components/aida-suggestions-panel';

const ProjectDetailsViewV2: React.FC<{ projectId: string }> = ({ projectId }) => {
  const { isMobile } = useResponsive();
  const [selectedFiles, setSelectedFiles] = useState<Resource[]>([]);
  // Store transcript data for future use (e.g., saving to notes, AI analysis, export)
  const [transcriptData, setTranscriptData] = useState<LiveTranscriptionSegment[]>([]);
  const isLiveTranscription = useAppSelector(selectIsLiveTranscription);

  // Reset selected files when projectId changes
  useEffect(() => {
    setSelectedFiles([]);
  }, [projectId]);

  // Handle transcript data from live transcription
  const handleTranscriptData = useCallback((segments: LiveTranscriptionSegment[]) => {
    setTranscriptData(segments);
    // You can now use this transcript data for various purposes:
    // - Save to project notes
    // - Send to AI for analysis
    // - Export as a file
    // - Display in other components
    console.log('Received transcript data:', segments);
  }, []);

  return (
    <Box
      sx={{
        height: {
          xs: 'calc(100vh - var(--layout-header-mobile-height))',
          md: 'calc(100vh - var(--layout-header-desktop-height))',
        },
        overflow: {
          xs: 'auto',
          md: 'hidden',
        },
        display: 'flex',
        flexDirection: {
          xs: 'column',
          md: 'row',
        },
        gap: 2,
        position: 'fixed',
        px: 2,
        pb: 2,
        top: {
          xs: 'var(--layout-header-mobile-height)',
          md: 'var(--layout-header-desktop-height)',
        },
        left: 0,
        right: 0,
        bottom: 0,
      }}
    >
      {isLiveTranscription ? (
        <LiveTranscriptionPanel onTranscriptData={handleTranscriptData} />
      ) : (
        <FilesPanel
          selectedFiles={selectedFiles}
          onFilesSelected={setSelectedFiles}
          projectId={projectId}
        />
      )}
      {isMobile ? (
        <>
          <NotesPanel projectId={projectId} />
          {isLiveTranscription ? (
            <AidaSuggestionsPanel projectId={projectId} />
          ) : (
            <ChatPanel selectedFiles={selectedFiles} projectId={projectId} />
          )}
        </>
      ) : (
        <>
          {isLiveTranscription ? (
            <AidaSuggestionsPanel projectId={projectId} />
          ) : (
            <ChatPanel selectedFiles={selectedFiles} projectId={projectId} />
          )}

          <NotesPanel projectId={projectId} />
        </>
      )}
    </Box>
  );
};

export default ProjectDetailsViewV2;
